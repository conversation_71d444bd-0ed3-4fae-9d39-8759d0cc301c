#!/usr/bin/env python3
"""
Test script to verify all integration test dependencies are working
"""

def test_imports():
    """Test all required imports for integration_test.py"""
    try:
        import requests
        print("✅ requests imported successfully")
        
        import json
        print("✅ json imported successfully")
        
        import time
        print("✅ time imported successfully")
        
        import websocket
        print(f"✅ websocket imported successfully (version: {getattr(websocket, '__version__', 'unknown')})")
        
        import threading
        print("✅ threading imported successfully")
        
        import psycopg2
        print("✅ psycopg2 imported successfully")
        
        from datetime import datetime
        print("✅ datetime imported successfully")
        
        import sys
        print("✅ sys imported successfully")
        
        print("\n🎉 All integration test dependencies are working correctly!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_websocket_functionality():
    """Test basic WebSocket functionality"""
    try:
        import websocket
        
        # Test WebSocket class
        ws = websocket.WebSocket()
        print("✅ WebSocket class instantiated successfully")
        
        # Test WebSocketApp class (used in integration_test.py)
        app = websocket.WebSocketApp("ws://example.com")
        print("✅ WebSocketApp class instantiated successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ WebSocket functionality error: {e}")
        return False

if __name__ == "__main__":
    print("Testing integration test dependencies...\n")
    
    imports_ok = test_imports()
    websocket_ok = test_websocket_functionality()
    
    if imports_ok and websocket_ok:
        print("\n✅ All tests passed! integration_test.py should work correctly.")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
