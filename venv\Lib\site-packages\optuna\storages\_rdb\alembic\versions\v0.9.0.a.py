"""empty message

Revision ID: v0.9.0.a
Revises:
Create Date: 2019-03-12 12:30:31.178819

"""

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "v0.9.0.a"
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "studies",
        sa.<PERSON>umn("study_id", sa.Integer(), nullable=False),
        sa.<PERSON>umn("study_name", sa.String(length=512), nullable=False),
        sa.<PERSON>umn(
            "direction",
            sa.Enum("NOT_SET", "MINIMIZE", "MAXIMIZE", name="studydirection"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("study_id"),
    )
    op.create_index(op.f("ix_studies_study_name"), "studies", ["study_name"], unique=True)
    op.create_table(
        "version_info",
        sa.Column("version_info_id", sa.Integer(), autoincrement=False, nullable=False),
        sa.Column("schema_version", sa.Integer(), nullable=True),
        sa.Column("library_version", sa.String(length=256), nullable=True),
        sa.CheckConstraint("version_info_id=1"),
        sa.PrimaryKeyConstraint("version_info_id"),
    )
    op.create_table(
        "study_system_attributes",
        sa.Column("study_system_attribute_id", sa.Integer(), nullable=False),
        sa.Column("study_id", sa.Integer(), nullable=True),
        sa.Column("key", sa.String(length=512), nullable=True),
        sa.Column("value_json", sa.String(length=2048), nullable=True),
        sa.ForeignKeyConstraint(["study_id"], ["studies.study_id"]),
        sa.PrimaryKeyConstraint("study_system_attribute_id"),
        sa.UniqueConstraint("study_id", "key"),
    )
    op.create_table(
        "study_user_attributes",
        sa.Column("study_user_attribute_id", sa.Integer(), nullable=False),
        sa.Column("study_id", sa.Integer(), nullable=True),
        sa.Column("key", sa.String(length=512), nullable=True),
        sa.Column("value_json", sa.String(length=2048), nullable=True),
        sa.ForeignKeyConstraint(["study_id"], ["studies.study_id"]),
        sa.PrimaryKeyConstraint("study_user_attribute_id"),
        sa.UniqueConstraint("study_id", "key"),
    )
    op.create_table(
        "trials",
        sa.Column("trial_id", sa.Integer(), nullable=False),
        sa.Column("study_id", sa.Integer(), nullable=True),
        sa.Column(
            "state",
            sa.Enum("RUNNING", "COMPLETE", "PRUNED", "FAIL", name="trialstate"),
            nullable=False,
        ),
        sa.Column("value", sa.Float(), nullable=True),
        sa.Column("datetime_start", sa.DateTime(), nullable=True),
        sa.Column("datetime_complete", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(["study_id"], ["studies.study_id"]),
        sa.PrimaryKeyConstraint("trial_id"),
    )
    op.create_table(
        "trial_params",
        sa.Column("param_id", sa.Integer(), nullable=False),
        sa.Column("trial_id", sa.Integer(), nullable=True),
        sa.Column("param_name", sa.String(length=512), nullable=True),
        sa.Column("param_value", sa.Float(), nullable=True),
        sa.Column("distribution_json", sa.String(length=2048), nullable=True),
        sa.ForeignKeyConstraint(["trial_id"], ["trials.trial_id"]),
        sa.PrimaryKeyConstraint("param_id"),
        sa.UniqueConstraint("trial_id", "param_name"),
    )
    op.create_table(
        "trial_system_attributes",
        sa.Column("trial_system_attribute_id", sa.Integer(), nullable=False),
        sa.Column("trial_id", sa.Integer(), nullable=True),
        sa.Column("key", sa.String(length=512), nullable=True),
        sa.Column("value_json", sa.String(length=2048), nullable=True),
        sa.ForeignKeyConstraint(["trial_id"], ["trials.trial_id"]),
        sa.PrimaryKeyConstraint("trial_system_attribute_id"),
        sa.UniqueConstraint("trial_id", "key"),
    )
    op.create_table(
        "trial_user_attributes",
        sa.Column("trial_user_attribute_id", sa.Integer(), nullable=False),
        sa.Column("trial_id", sa.Integer(), nullable=True),
        sa.Column("key", sa.String(length=512), nullable=True),
        sa.Column("value_json", sa.String(length=2048), nullable=True),
        sa.ForeignKeyConstraint(["trial_id"], ["trials.trial_id"]),
        sa.PrimaryKeyConstraint("trial_user_attribute_id"),
        sa.UniqueConstraint("trial_id", "key"),
    )
    op.create_table(
        "trial_values",
        sa.Column("trial_value_id", sa.Integer(), nullable=False),
        sa.Column("trial_id", sa.Integer(), nullable=True),
        sa.Column("step", sa.Integer(), nullable=True),
        sa.Column("value", sa.Float(), nullable=True),
        sa.ForeignKeyConstraint(["trial_id"], ["trials.trial_id"]),
        sa.PrimaryKeyConstraint("trial_value_id"),
        sa.UniqueConstraint("trial_id", "step"),
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table("trial_values")
    op.drop_table("trial_user_attributes")
    op.drop_table("trial_system_attributes")
    op.drop_table("trial_params")
    op.drop_table("trials")
    op.drop_table("study_user_attributes")
    op.drop_table("study_system_attributes")
    op.drop_table("version_info")
    op.drop_index(op.f("ix_studies_study_name"), table_name="studies")
    op.drop_table("studies")
    # ### end Alembic commands ###
