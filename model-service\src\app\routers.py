"""
API routes for case management.
"""
from fastapi import API<PERSON>outer, Depends, HTTPException, status
from fastapi.security import <PERSON>Auth2<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta, timezone
import jwt
from passlib.context import Crypt<PERSON>ontext

from app.models import <PERSON>r, Case, AuditLog, CaseStatus, CaseTag
from app.schemas import (
    CaseCreate, CaseUpdate, CaseResponse,
    Token, TokenData,
    CaseStatsResponse
)
from app.database import get_db

# JWT configuration
SECRET_KEY = "your-secret-key"  # In production, use environment variable
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/login")

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Create router
router = APIRouter()

# Helper functions


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create JWT access token"""
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = (datetime.now(timezone.utc) +
                  timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES))

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

    return encoded_jwt


async def get_current_user(token: str = Depends(oauth2_scheme),
                           db: Session = Depends(get_db)):
    """Get current user from JWT token"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")

        if username is None:
            raise credentials_exception

        token_data = TokenData(username=username)
    except jwt.PyJWTError:
        raise credentials_exception

    user = db.query(User).filter(User.username == token_data.username).first()

    if user is None:
        raise credentials_exception

    return user


# Auth routes


@router.post("/auth/login", response_model=Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends(),
                db: Session = Depends(get_db)):
    """Login and get access token"""
    user = db.query(User).filter(User.username == form_data.username).first()

    if not user or not verify_password(form_data.password,
                                       str(user.password_hash)):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    return {"access_token": access_token, "token_type": "bearer"}


# Case routes


@router.post("/cases", response_model=CaseResponse)
async def create_case(
    case: CaseCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new case"""
    db_case = Case(
        transaction_id=case.transaction_id,
        user_id=current_user.id,
        tag=case.tag,
        comment=case.comment,
        status=case.status
    )

    db.add(db_case)
    db.commit()
    db.refresh(db_case)

    # Create audit log
    audit_log = AuditLog(
        case_id=db_case.id,
        action="CREATE",
        metadata={"user_id": current_user.id}
    )

    db.add(audit_log)
    db.commit()

    return db_case


@router.get("/cases/{case_id}", response_model=CaseResponse)
async def get_case(
    case_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a case by ID"""
    case = db.query(Case).filter(Case.id == case_id).first()

    if case is None:
        raise HTTPException(status_code=404, detail="Case not found")

    return case


@router.put("/cases/{case_id}", response_model=CaseResponse)
async def update_case(
    case_id: int,
    case_update: CaseUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a case"""
    db_case = db.query(Case).filter(Case.id == case_id).first()

    if db_case is None:
        raise HTTPException(status_code=404, detail="Case not found")

    # Update fields
    if case_update.tag is not None:
        setattr(db_case, 'tag', case_update.tag)

    if case_update.comment is not None:
        setattr(db_case, 'comment', case_update.comment)

    if case_update.status is not None:
        setattr(db_case, 'status', case_update.status)

    setattr(db_case, 'updated_at', datetime.now(timezone.utc))

    db.commit()
    db.refresh(db_case)

    # Create audit log
    updated_fields = case_update.model_dump(exclude_unset=True)
    audit_log = AuditLog(
        case_id=db_case.id,
        action="UPDATE",
        metadata={"user_id": current_user.id, "updated_fields": updated_fields}
    )

    db.add(audit_log)
    db.commit()

    return db_case


@router.get("/cases", response_model=List[CaseResponse])
async def list_cases(
    case_status: Optional[CaseStatus] = None,
    transaction_id: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """List cases with optional filters"""
    query = db.query(Case)

    if case_status:
        query = query.filter(Case.status == case_status)

    if transaction_id:
        query = query.filter(Case.transaction_id == transaction_id)

    cases = query.all()
    return cases


@router.get("/cases/stats", response_model=CaseStatsResponse)
async def get_case_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get case statistics"""
    # Count by status
    status_counts = {}
    for case_status in CaseStatus:
        count = db.query(Case).filter(Case.status == case_status).count()
        status_counts[case_status.value] = count

    # Count by tag
    tag_counts = {}
    for tag in CaseTag:
        count = db.query(Case).filter(Case.tag == tag).count()
        tag_counts[tag.value] = count

    return {
        "status_counts": status_counts,
        "tag_counts": tag_counts,
        "total_cases": db.query(Case).count()
    }


# Helper function for password verification


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify password against hash"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """Hash password"""
    return pwd_context.hash(password)

