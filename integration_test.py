#!/usr/bin/env python3
"""
Fraud Detection Platform - Comprehensive Integration Test
Tests all components and their interactions end-to-end
"""

import requests
import json
import time
import websocket
import threading
import psycopg2
from datetime import datetime
import sys


class FraudPlatformIntegrationTest:
    def __init__(self):
        self.base_urls = {
            'model': 'http://localhost:8000',
            'ingest': 'http://localhost:9000',
            'dashboard': 'http://localhost:3000'
        }
        self.db_config = {
            'host': 'localhost',
            'port': 5432,
            'database': 'fraud_detection',
            'user': 'postgres',
            'password': 'postgres'
        }
        self.test_results = []
        self.websocket_messages = []

    def log_test(self, test_name, success, message=""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if message:
            print(f"    {message}")

        self.test_results.append({
            'test': test_name,
            'success': success,
            'message': message,
            'timestamp': datetime.now().isoformat()
        })

    def test_database_connection(self):
        """Test PostgreSQL database connection and schema"""
        try:
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()

            # Test basic connection
            cursor.execute("SELECT version();")
            cursor.fetchone()[0]  # Verify connection works

            # Test schema exists
            cursor.execute("""
                SELECT table_name FROM information_schema.tables
                WHERE table_schema = 'fraud' AND table_type = 'BASE TABLE'
            """)
            tables = [row[0] for row in cursor.fetchall()]

            expected_tables = ['transactions', 'fraud_cases', 'alerts',
                               'users', 'model_predictions']
            missing_tables = set(expected_tables) - set(tables)

            if missing_tables:
                self.log_test("Database Schema", False,
                              f"Missing tables: {missing_tables}")
                return False

            # Test sample data
            cursor.execute("SELECT COUNT(*) FROM fraud.transactions")
            transaction_count = cursor.fetchone()[0]

            cursor.close()
            conn.close()

            self.log_test("Database Connection", True,
                          f"Connected to PostgreSQL, {transaction_count} transactions")
            return True

        except Exception as e:
            self.log_test("Database Connection", False, str(e))
            return False

    def test_model_service(self):
        """Test Model Service functionality"""
        try:
            # Test health endpoint
            response = requests.get(f"{self.base_urls['model']}/health",
                                    timeout=10)
            if response.status_code != 200:
                self.log_test("Model Service Health", False,
                              f"HTTP {response.status_code}")
                return False

            health_data = response.json()
            self.log_test("Model Service Health", True,
                          f"Status: {health_data['status']}")

            # Test model info
            response = requests.get(f"{self.base_urls['model']}/model/info",
                                    timeout=10)
            if response.status_code == 200:
                info = response.json()
                self.log_test("Model Info", True,
                              f"Model type: {info['model_type']}")

            # Test fraud scoring
            test_transaction = {
                "transactions": [{
                    "transaction_id": "test_integration",
                    "step": 1,
                    "type": "TRANSFER",
                    "amount": 50000.0,
                    "nameOrig": "C123456789",
                    "oldbalanceOrg": 60000.0,
                    "newbalanceOrig": 10000.0,
                    "nameDest": "C987654321",
                    "oldbalanceDest": 5000.0,
                    "newbalanceDest": 55000.0
                }]
            }

            response = requests.post(
                f"{self.base_urls['model']}/score",
                json=test_transaction,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                risk_score = result['results'][0]['risk']
                self.log_test("Fraud Scoring", True,
                              f"Risk score: {risk_score:.3f}")
                return True
            else:
                self.log_test("Fraud Scoring", False,
                              f"HTTP {response.status_code}")
                return False

        except Exception as e:
            self.log_test("Model Service", False, str(e))
            return False

    def test_ingest_service(self):
        """Test Ingest Service functionality"""
        try:
            # Test health endpoint
            response = requests.get(f"{self.base_urls['ingest']}/health",
                                    timeout=10)
            if response.status_code != 200:
                self.log_test("Ingest Service Health", False,
                              f"HTTP {response.status_code}")
                return False

            health_data = response.json()
            self.log_test("Ingest Service Health", True,
                          f"Status: {health_data['status']}")

            # Test transaction processing
            test_transaction = {
                "transaction_id": f"test_ingest_{int(time.time())}",
                "step": 1,
                "type": "PAYMENT",
                "amount": 100.0,
                "nameOrig": "C123456789",
                "oldbalanceOrg": 1000.0,
                "newbalanceOrig": 900.0,
                "nameDest": "M987654321",
                "oldbalanceDest": 0.0,
                "newbalanceDest": 100.0
            }

            response = requests.post(
                f"{self.base_urls['ingest']}/process",
                json=test_transaction,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                risk_score = result.get('risk_score', 'N/A')
                self.log_test("Transaction Processing", True,
                              f"Processed with risk score: {risk_score}")
                return True
            else:
                self.log_test("Transaction Processing", False,
                              f"HTTP {response.status_code}")
                return False

        except Exception as e:
            self.log_test("Ingest Service", False, str(e))
            return False

    def test_websocket_connection(self):
        """Test WebSocket real-time updates"""
        try:
            ws_url = "ws://localhost:9000/ws/txns"

            def on_message(_, message):
                self.websocket_messages.append(json.loads(message))

            def on_error(_, error):
                print(f"WebSocket error: {error}")

            def on_close(*_):
                pass

            # Create WebSocket connection
            ws = websocket.WebSocketApp(
                ws_url,
                on_message=on_message,
                on_error=on_error,
                on_close=on_close
            )

            # Start WebSocket in a separate thread
            ws_thread = threading.Thread(target=ws.run_forever)
            ws_thread.daemon = True
            ws_thread.start()

            # Wait a moment for connection
            time.sleep(2)

            # Send a test transaction to trigger WebSocket message
            test_transaction = {
                "transaction_id": f"ws_test_{int(time.time())}",
                "step": 1,
                "type": "TRANSFER",
                "amount": 75000.0,
                "nameOrig": "C111111111",
                "oldbalanceOrg": 80000.0,
                "newbalanceOrig": 5000.0,
                "nameDest": "C222222222",
                "oldbalanceDest": 1000.0,
                "newbalanceDest": 76000.0
            }

            requests.post(
                f"{self.base_urls['ingest']}/process",
                json=test_transaction,
                timeout=10
            )

            # Wait for WebSocket message
            time.sleep(3)
            ws.close()

            if self.websocket_messages:
                msg_count = len(self.websocket_messages)
                self.log_test("WebSocket Connection", True,
                              f"Received {msg_count} messages")
                return True
            else:
                self.log_test("WebSocket Connection", False,
                              "No messages received")
                return False

        except Exception as e:
            self.log_test("WebSocket Connection", False, str(e))
            return False

    def test_dashboard_accessibility(self):
        """Test Dashboard accessibility"""
        try:
            response = requests.get(self.base_urls['dashboard'], timeout=10)
            if response.status_code == 200:
                self.log_test("Dashboard Accessibility", True,
                              "Dashboard is accessible")
                return True
            else:
                self.log_test("Dashboard Accessibility", False,
                              f"HTTP {response.status_code}")
                return False
        except Exception as e:
            self.log_test("Dashboard Accessibility", False, str(e))
            return False

    def test_end_to_end_workflow(self):
        """Test complete end-to-end fraud detection workflow"""
        try:
            # Generate a high-risk transaction
            high_risk_transaction = {
                "transaction_id": f"e2e_test_{int(time.time())}",
                "step": 1,
                "type": "CASH_OUT",
                "amount": 200000.0,
                "nameOrig": "C999999999",
                "oldbalanceOrg": 250000.0,
                "newbalanceOrig": 50000.0,
                "nameDest": "M888888888",
                "oldbalanceDest": 0.0,
                "newbalanceDest": 200000.0
            }

            # Process through ingest service
            response = requests.post(
                f"{self.base_urls['ingest']}/process",
                json=high_risk_transaction,
                timeout=10
            )

            if response.status_code != 200:
                self.log_test("End-to-End Workflow", False,
                              "Failed to process transaction")
                return False

            # Verify transaction was stored in database
            conn = psycopg2.connect(**self.db_config)
            cursor = conn.cursor()
            cursor.execute(
                "SELECT risk_score FROM fraud.transactions "
                "WHERE transaction_id = %s",
                (high_risk_transaction['transaction_id'],)
            )
            db_result = cursor.fetchone()
            cursor.close()
            conn.close()

            if db_result:
                db_risk_score = float(db_result[0])
                self.log_test("End-to-End Workflow", True,
                              f"Transaction processed and stored "
                              f"(Risk: {db_risk_score:.3f})")
                return True
            else:
                self.log_test("End-to-End Workflow", False,
                              "Transaction not found in database")
                return False

        except Exception as e:
            self.log_test("End-to-End Workflow", False, str(e))
            return False

    def test_api_documentation(self):
        """Test API documentation accessibility"""
        try:
            # Test Model Service docs
            response = requests.get(f"{self.base_urls['model']}/docs",
                                    timeout=10)
            model_docs_ok = response.status_code == 200

            # Test Ingest Service docs
            response = requests.get(f"{self.base_urls['ingest']}/docs",
                                    timeout=10)
            ingest_docs_ok = response.status_code == 200

            if model_docs_ok and ingest_docs_ok:
                self.log_test("API Documentation", True,
                              "All API docs accessible")
                return True
            else:
                self.log_test("API Documentation", False,
                              "Some API docs not accessible")
                return False

        except Exception as e:
            self.log_test("API Documentation", False, str(e))
            return False

    def run_all_tests(self):
        """Run all integration tests"""
        print("🚀 FRAUD DETECTION PLATFORM - INTEGRATION TEST SUITE")
        print("=" * 70)

        tests = [
            ("Database Connection", self.test_database_connection),
            ("Model Service", self.test_model_service),
            ("Ingest Service", self.test_ingest_service),
            ("WebSocket Connection", self.test_websocket_connection),
            ("Dashboard Accessibility", self.test_dashboard_accessibility),
            ("End-to-End Workflow", self.test_end_to_end_workflow),
            ("API Documentation", self.test_api_documentation)
        ]

        passed_tests = 0
        total_tests = len(tests)

        for test_name, test_func in tests:
            print(f"\n🔍 Running {test_name}...")
            if test_func():
                passed_tests += 1

        # Print summary
        print("\n" + "=" * 70)
        print("📊 INTEGRATION TEST SUMMARY")
        print("=" * 70)
        print(f"Tests Passed: {passed_tests}/{total_tests}")
        print(f"Success Rate: {passed_tests/total_tests*100:.1f}%")

        if passed_tests == total_tests:
            print("🎉 ALL INTEGRATION TESTS PASSED!")
            print("The fraud detection platform is fully functional "
                  "and integrated.")
        elif passed_tests >= total_tests * 0.7:
            print("✅ MOST TESTS PASSED!")
            print("Core functionality is working. Some components "
                  "may need attention.")
        else:
            print("⚠️ MULTIPLE FAILURES DETECTED!")
            print("Please review the failed tests and fix issues "
                  "before deployment.")

        # Save test results
        with open('integration_test_results.json', 'w') as f:
            json.dump({
                'summary': {
                    'total_tests': total_tests,
                    'passed_tests': passed_tests,
                    'success_rate': passed_tests/total_tests,
                    'timestamp': datetime.now().isoformat()
                },
                'test_results': self.test_results
            }, f, indent=2)

        print("\n📄 Detailed results saved to: integration_test_results.json")

        return passed_tests == total_tests


def main():
    """Main function"""
    tester = FraudPlatformIntegrationTest()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
